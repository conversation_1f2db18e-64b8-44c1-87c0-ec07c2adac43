# 几何图形平滑化处理功能

## 概述

本功能使用 JTS (Java Topology Suite) 库来处理降水落区文件中的几何图形，自动去除不规则的"尖刺"，使多边形边界更加平滑。

## 功能特性

1. **自动平滑化**: 使用 Douglas-Peucker 算法和缓冲区操作来平滑多边形边界
2. **去除尖刺**: 自动识别并去除几何图形中的不规则突出部分
3. **保持有效性**: 确保处理后的几何图形仍然是有效的多边形
4. **参数可调**: 支持自定义简化容差和缓冲区距离

## 实现文件

### 1. GeometryUtils.java
位置: `src/main/java/com/yf/exam/modules/weather/utils/GeometryUtils.java`

主要方法:
- `smoothPolygonCoordinates()`: 平滑化多边形坐标
- `isValidPolygon()`: 验证多边形有效性
- `calculatePolygonArea()`: 计算多边形面积

### 2. WeatherFileUploadController.java (修改)
位置: `src/main/java/com/yf/exam/modules/weather/controller/WeatherFileUploadController.java`

修改内容:
- 在 `addContourToData()` 方法中集成几何图形平滑化处理
- 只对坐标点数量超过10个的多边形进行平滑化处理

### 3. pom.xml (修改)
添加了 JTS 依赖:
```xml
<dependency>
    <groupId>org.locationtech.jts</groupId>
    <artifactId>jts-core</artifactId>
    <version>1.19.0</version>
</dependency>
```

## 使用方法

### 自动处理
当用户上传降水落区文件时，系统会自动对解析出的几何图形进行平滑化处理。处理过程对用户透明，无需额外操作。

### 手动调用
```java
// 创建带有"尖刺"的坐标列表
List<List<Double>> coordinates = Arrays.asList(
    Arrays.asList(116.0, 40.0),
    Arrays.asList(116.1, 40.0),
    Arrays.asList(116.05, 40.05), // 尖刺点
    Arrays.asList(116.1, 40.1),
    Arrays.asList(116.0, 40.1),
    Arrays.asList(116.0, 40.0)
);

// 执行平滑化处理
List<List<Double>> smoothed = GeometryUtils.smoothPolygonCoordinates(coordinates);
```

## 算法原理

### 1. Douglas-Peucker 简化
- 使用 Douglas-Peucker 算法去除冗余的坐标点
- 默认简化容差: 0.001度
- 保持多边形的基本形状特征

### 2. 缓冲区平滑化
- 先向外扩展一个小的缓冲区
- 再向内收缩相同大小的缓冲区
- 这个过程可以平滑尖锐的角度和小的突出部分
- 默认缓冲区距离: 0.0005度

### 3. 有效性验证
- 处理后验证几何图形的有效性
- 如果平滑化失败，返回简化后的结果
- 如果简化也失败，返回原始坐标

## 参数调整

可以通过修改 `GeometryUtils` 类中的常量来调整处理效果:

```java
// 简化容差 - 值越大，简化程度越高
private static final double DEFAULT_SIMPLIFY_TOLERANCE = 0.001;

// 缓冲区距离 - 值越大，平滑效果越明显
private static final double DEFAULT_BUFFER_DISTANCE = 0.0005;
```

## 性能考虑

1. **条件处理**: 只对坐标点数量超过10个的多边形进行平滑化处理
2. **异常处理**: 如果处理失败，自动回退到原始坐标
3. **内存优化**: 使用坐标副本避免引用问题

## 日志记录

系统会记录以下信息:
- 平滑化处理的开始和完成
- 原始坐标点数和处理后坐标点数
- 处理失败时的警告信息

## 测试验证

可以使用 `GeometryUtilsExample.java` 来测试和验证功能:
- 创建带有尖刺的测试多边形
- 演示平滑化效果
- 比较处理前后的面积变化

## 注意事项

1. **坐标系统**: 假设输入坐标为经纬度 (WGS84)
2. **精度**: 处理精度适合气象数据的精度要求
3. **兼容性**: 与现有的 GeoJSON 格式完全兼容
4. **错误处理**: 处理失败时不会影响文件上传流程

## 未来改进

1. 支持更多的平滑化算法
2. 提供用户可配置的参数界面
3. 添加处理效果的可视化预览
4. 支持批量处理多个文件
