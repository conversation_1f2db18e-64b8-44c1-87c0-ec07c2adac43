package com.yf.exam.modules.weather.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * GeometryUtils 使用示例
 * 演示如何使用JTS库处理几何图形的平滑化
 */
@Slf4j
public class GeometryUtilsExample {

    public static void main(String[] args) {
        log.info("=== GeometryUtils 使用示例 ===");
        
        // 示例1：创建一个带有"尖刺"的多边形
        List<List<Double>> originalCoordinates = createSpikyPolygon();
        log.info("原始多边形坐标点数: {}", originalCoordinates.size());
        
        // 打印原始坐标
        log.info("原始坐标:");
        for (int i = 0; i < originalCoordinates.size(); i++) {
            List<Double> point = originalCoordinates.get(i);
            log.info("  点{}: [{}, {}]", i + 1, point.get(0), point.get(1));
        }
        
        // 执行平滑化处理
        List<List<Double>> smoothedCoordinates = GeometryUtils.smoothPolygonCoordinates(originalCoordinates);
        log.info("平滑化后坐标点数: {}", smoothedCoordinates.size());
        
        // 打印平滑化后的坐标
        log.info("平滑化后坐标:");
        for (int i = 0; i < smoothedCoordinates.size(); i++) {
            List<Double> point = smoothedCoordinates.get(i);
            log.info("  点{}: [{}, {}]", i + 1, point.get(0), point.get(1));
        }
        
        // 验证多边形有效性
        boolean isValid = GeometryUtils.isValidPolygon(smoothedCoordinates);
        log.info("平滑化后的多边形是否有效: {}", isValid);
        
        // 计算面积
        double originalArea = GeometryUtils.calculatePolygonArea(originalCoordinates);
        double smoothedArea = GeometryUtils.calculatePolygonArea(smoothedCoordinates);
        log.info("原始多边形面积: {} 平方度", originalArea);
        log.info("平滑化后多边形面积: {} 平方度", smoothedArea);
        log.info("面积变化: {}%", ((smoothedArea - originalArea) / originalArea) * 100);
        
        log.info("=== 示例完成 ===");
    }
    
    /**
     * 创建一个带有"尖刺"的多边形坐标
     * 模拟实际降水落区文件中可能出现的不规则形状
     */
    private static List<List<Double>> createSpikyPolygon() {
        List<List<Double>> coordinates = new ArrayList<>();
        
        // 基本矩形
        coordinates.add(Arrays.asList(116.0, 40.0));
        coordinates.add(Arrays.asList(116.2, 40.0));
        
        // 添加一个尖刺
        coordinates.add(Arrays.asList(116.25, 40.02));
        coordinates.add(Arrays.asList(116.22, 40.01));
        coordinates.add(Arrays.asList(116.24, 40.03));
        coordinates.add(Arrays.asList(116.21, 40.02));
        
        coordinates.add(Arrays.asList(116.2, 40.1));
        coordinates.add(Arrays.asList(116.15, 40.12));
        
        // 添加另一个尖刺
        coordinates.add(Arrays.asList(116.12, 40.15));
        coordinates.add(Arrays.asList(116.14, 40.13));
        coordinates.add(Arrays.asList(116.11, 40.14));
        
        coordinates.add(Arrays.asList(116.1, 40.1));
        coordinates.add(Arrays.asList(116.0, 40.1));
        
        // 添加左侧尖刺
        coordinates.add(Arrays.asList(115.98, 40.08));
        coordinates.add(Arrays.asList(115.99, 40.06));
        coordinates.add(Arrays.asList(115.97, 40.07));
        
        coordinates.add(Arrays.asList(116.0, 40.0)); // 闭合
        
        return coordinates;
    }
    
    /**
     * 演示不同参数对平滑化效果的影响
     */
    public static void demonstrateParameters() {
        log.info("=== 参数影响演示 ===");
        
        List<List<Double>> coordinates = createSpikyPolygon();
        log.info("原始坐标点数: {}", coordinates.size());
        
        // 测试不同的简化容差
        double[] tolerances = {0.0001, 0.001, 0.01};
        for (double tolerance : tolerances) {
            List<List<Double>> result = GeometryUtils.smoothPolygonCoordinates(
                coordinates, tolerance, 0.0005);
            log.info("简化容差 {}: 结果点数 {}", tolerance, result.size());
        }
        
        // 测试不同的缓冲区距离
        double[] bufferDistances = {0.0001, 0.0005, 0.001};
        for (double bufferDistance : bufferDistances) {
            List<List<Double>> result = GeometryUtils.smoothPolygonCoordinates(
                coordinates, 0.001, bufferDistance);
            log.info("缓冲区距离 {}: 结果点数 {}", bufferDistance, result.size());
        }
        
        log.info("=== 参数演示完成 ===");
    }
}
