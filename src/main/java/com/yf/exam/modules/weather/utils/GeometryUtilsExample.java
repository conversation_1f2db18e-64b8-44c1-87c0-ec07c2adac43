package com.yf.exam.modules.weather.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * GeometryUtils 使用示例
 * 演示如何使用JTS库处理几何图形的平滑化
 */
@Slf4j
public class GeometryUtilsExample {

    public static void main(String[] args) {
        log.info("=== GeometryUtils 使用示例 ===");

        // 示例1：创建一个带有"尖刺"和相交区域的多边形
        List<List<Double>> originalCoordinates = createSpikyPolygonWithIntersections();
        log.info("原始多边形坐标点数: {}", originalCoordinates.size());

        // 打印原始坐标
        log.info("原始坐标:");
        for (int i = 0; i < Math.min(originalCoordinates.size(), 10); i++) {
            List<Double> point = originalCoordinates.get(i);
            log.info("  点{}: [{}, {}]", i + 1, point.get(0), point.get(1));
        }
        if (originalCoordinates.size() > 10) {
            log.info("  ... 还有 {} 个点", originalCoordinates.size() - 10);
        }

        // 执行综合处理（删除小相交区域 + 平滑化）
        List<List<Double>> processedCoordinates = GeometryUtils.processPolygonComprehensively(originalCoordinates);
        log.info("综合处理后坐标点数: {}", processedCoordinates.size());

        // 验证多边形有效性
        boolean isValid = GeometryUtils.isValidPolygon(processedCoordinates);
        log.info("处理后的多边形是否有效: {}", isValid);

        // 计算面积
        double originalArea = GeometryUtils.calculatePolygonArea(originalCoordinates);
        double processedArea = GeometryUtils.calculatePolygonArea(processedCoordinates);
        log.info("原始多边形面积: {} 平方度", originalArea);
        log.info("处理后多边形面积: {} 平方度", processedArea);
        log.info("面积变化: {}%", ((processedArea - originalArea) / originalArea) * 100);

        // 单独测试删除小相交区域功能
        log.info("\n=== 测试删除小相交区域功能 ===");
        List<List<Double>> cleanedCoordinates = GeometryUtils.removeSmallIntersectionAreas(originalCoordinates);
        log.info("删除小相交区域后坐标点数: {}", cleanedCoordinates.size());
        double cleanedArea = GeometryUtils.calculatePolygonArea(cleanedCoordinates);
        log.info("删除小相交区域后面积: {} 平方度", cleanedArea);
        
        log.info("=== 示例完成 ===");
    }
    
    /**
     * 创建一个带有"尖刺"和相交区域的多边形坐标
     * 模拟实际降水落区文件中可能出现的线段相交形成小区域的情况
     */
    private static List<List<Double>> createSpikyPolygonWithIntersections() {
        List<List<Double>> coordinates = new ArrayList<>();

        // 基本多边形，故意设计一些线段相交的情况
        coordinates.add(Arrays.asList(116.0, 40.0));
        coordinates.add(Arrays.asList(116.2, 40.0));
        coordinates.add(Arrays.asList(116.25, 40.02)); // 尖刺点
        coordinates.add(Arrays.asList(116.22, 40.01));
        coordinates.add(Arrays.asList(116.24, 40.03)); // 另一个尖刺点
        coordinates.add(Arrays.asList(116.21, 40.02));
        coordinates.add(Arrays.asList(116.2, 40.1));

        // 创建一个会与前面线段相交的路径
        coordinates.add(Arrays.asList(116.15, 40.12));
        coordinates.add(Arrays.asList(116.12, 40.15)); // 这里会形成相交
        coordinates.add(Arrays.asList(116.18, 40.08)); // 回折，可能形成小三角形
        coordinates.add(Arrays.asList(116.14, 40.13));
        coordinates.add(Arrays.asList(116.11, 40.14));

        coordinates.add(Arrays.asList(116.1, 40.1));
        coordinates.add(Arrays.asList(116.05, 40.12)); // 添加更多可能相交的点
        coordinates.add(Arrays.asList(116.08, 40.08));
        coordinates.add(Arrays.asList(116.0, 40.1));

        // 左侧也添加一些可能相交的点
        coordinates.add(Arrays.asList(115.98, 40.08));
        coordinates.add(Arrays.asList(115.95, 40.05)); // 可能形成小区域
        coordinates.add(Arrays.asList(115.99, 40.06));
        coordinates.add(Arrays.asList(115.97, 40.07));
        coordinates.add(Arrays.asList(115.99, 40.04)); // 回折点

        coordinates.add(Arrays.asList(116.0, 40.0)); // 闭合

        return coordinates;
    }

    /**
     * 创建一个简单的带有"尖刺"的多边形坐标（原版本）
     */
    private static List<List<Double>> createSpikyPolygon() {
        List<List<Double>> coordinates = new ArrayList<>();

        // 基本矩形
        coordinates.add(Arrays.asList(116.0, 40.0));
        coordinates.add(Arrays.asList(116.2, 40.0));

        // 添加一个尖刺
        coordinates.add(Arrays.asList(116.25, 40.02));
        coordinates.add(Arrays.asList(116.22, 40.01));
        coordinates.add(Arrays.asList(116.24, 40.03));
        coordinates.add(Arrays.asList(116.21, 40.02));

        coordinates.add(Arrays.asList(116.2, 40.1));
        coordinates.add(Arrays.asList(116.15, 40.12));

        // 添加另一个尖刺
        coordinates.add(Arrays.asList(116.12, 40.15));
        coordinates.add(Arrays.asList(116.14, 40.13));
        coordinates.add(Arrays.asList(116.11, 40.14));

        coordinates.add(Arrays.asList(116.1, 40.1));
        coordinates.add(Arrays.asList(116.0, 40.1));

        // 添加左侧尖刺
        coordinates.add(Arrays.asList(115.98, 40.08));
        coordinates.add(Arrays.asList(115.99, 40.06));
        coordinates.add(Arrays.asList(115.97, 40.07));

        coordinates.add(Arrays.asList(116.0, 40.0)); // 闭合

        return coordinates;
    }
    
    /**
     * 演示不同参数对平滑化效果的影响
     */
    public static void demonstrateParameters() {
        log.info("=== 参数影响演示 ===");
        
        List<List<Double>> coordinates = createSpikyPolygon();
        log.info("原始坐标点数: {}", coordinates.size());
        
        // 测试不同的简化容差
        double[] tolerances = {0.0001, 0.001, 0.01};
        for (double tolerance : tolerances) {
            List<List<Double>> result = GeometryUtils.smoothPolygonCoordinates(
                coordinates, tolerance, 0.0005);
            log.info("简化容差 {}: 结果点数 {}", tolerance, result.size());
        }
        
        // 测试不同的缓冲区距离
        double[] bufferDistances = {0.0001, 0.0005, 0.001};
        for (double bufferDistance : bufferDistances) {
            List<List<Double>> result = GeometryUtils.smoothPolygonCoordinates(
                coordinates, 0.001, bufferDistance);
            log.info("缓冲区距离 {}: 结果点数 {}", bufferDistance, result.size());
        }
        
        log.info("=== 参数演示完成 ===");
    }
}
