package com.yf.exam.modules.weather.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 几何图形处理演示类
 * 演示如何处理线段相交产生的小区域问题
 */
@Slf4j
public class GeometryProcessingDemo {

    public static void main(String[] args) {
        demonstrateIntersectionAreaRemoval();
    }

    /**
     * 演示删除线段相交产生的小区域功能
     */
    public static void demonstrateIntersectionAreaRemoval() {
        log.info("=== 线段相交小区域处理演示 ===");

        // 创建一个有线段相交问题的多边形
        List<List<Double>> problematicCoordinates = createProblematicPolygon();
        
        log.info("原始多边形:");
        log.info("  坐标点数: {}", problematicCoordinates.size());
        log.info("  面积: {} 平方度", GeometryUtils.calculatePolygonArea(problematicCoordinates));
        log.info("  是否有效: {}", GeometryUtils.isValidPolygon(problematicCoordinates));

        // 只删除小相交区域
        log.info("\n--- 步骤1: 删除小相交区域 ---");
        List<List<Double>> cleanedCoordinates = GeometryUtils.removeSmallIntersectionAreas(problematicCoordinates);
        log.info("删除小相交区域后:");
        log.info("  坐标点数: {}", cleanedCoordinates.size());
        log.info("  面积: {} 平方度", GeometryUtils.calculatePolygonArea(cleanedCoordinates));
        log.info("  是否有效: {}", GeometryUtils.isValidPolygon(cleanedCoordinates));

        // 只进行平滑化处理
        log.info("\n--- 步骤2: 平滑化处理 ---");
        List<List<Double>> smoothedCoordinates = GeometryUtils.smoothPolygonCoordinates(cleanedCoordinates);
        log.info("平滑化处理后:");
        log.info("  坐标点数: {}", smoothedCoordinates.size());
        log.info("  面积: {} 平方度", GeometryUtils.calculatePolygonArea(smoothedCoordinates));
        log.info("  是否有效: {}", GeometryUtils.isValidPolygon(smoothedCoordinates));

        // 综合处理
        log.info("\n--- 综合处理 ---");
        List<List<Double>> comprehensivelyProcessed = GeometryUtils.processPolygonComprehensively(problematicCoordinates);
        log.info("综合处理后:");
        log.info("  坐标点数: {}", comprehensivelyProcessed.size());
        log.info("  面积: {} 平方度", GeometryUtils.calculatePolygonArea(comprehensivelyProcessed));
        log.info("  是否有效: {}", GeometryUtils.isValidPolygon(comprehensivelyProcessed));

        // 超强力处理（用于最困难的情况）
        log.info("\n--- 超强力处理 ---");
        List<List<Double>> ultraProcessed = GeometryUtils.processPolygonUltraComprehensively(problematicCoordinates);
        log.info("超强力处理后:");
        log.info("  坐标点数: {}", ultraProcessed.size());
        log.info("  面积: {} 平方度", GeometryUtils.calculatePolygonArea(ultraProcessed));
        log.info("  是否有效: {}", GeometryUtils.isValidPolygon(ultraProcessed));

        // 强力清理测试
        log.info("\n--- 强力清理测试 ---");
        List<List<Double>> aggressivelyCleaned = GeometryUtils.aggressiveCleanIntersections(problematicCoordinates);
        log.info("强力清理后:");
        log.info("  坐标点数: {}", aggressivelyCleaned.size());
        log.info("  面积: {} 平方度", GeometryUtils.calculatePolygonArea(aggressivelyCleaned));
        log.info("  是否有效: {}", GeometryUtils.isValidPolygon(aggressivelyCleaned));

        // 比较结果
        log.info("\n--- 处理效果对比 ---");
        double originalArea = GeometryUtils.calculatePolygonArea(problematicCoordinates);
        double finalArea = GeometryUtils.calculatePolygonArea(comprehensivelyProcessed);
        double areaChange = ((finalArea - originalArea) / originalArea) * 100;
        
        log.info("坐标点数变化: {} -> {} (减少了 {} 个点)", 
            problematicCoordinates.size(), 
            comprehensivelyProcessed.size(),
            problematicCoordinates.size() - comprehensivelyProcessed.size());
        log.info("面积变化: {:.6f} -> {:.6f} (变化 {:.2f}%)", 
            originalArea, finalArea, areaChange);

        // 测试不同的面积阈值
        log.info("\n--- 不同面积阈值测试 ---");
        double[] thresholds = {0.00001, 0.0001, 0.001, 0.01};
        for (double threshold : thresholds) {
            List<List<Double>> result = GeometryUtils.removeSmallIntersectionAreas(problematicCoordinates, threshold);
            log.info("阈值 {}: 坐标点数 {}, 面积 {:.6f}", 
                threshold, result.size(), GeometryUtils.calculatePolygonArea(result));
        }

        log.info("\n=== 演示完成 ===");
    }

    /**
     * 创建一个有线段相交问题的多边形
     * 模拟实际降水落区文件中可能出现的情况
     */
    private static List<List<Double>> createProblematicPolygon() {
        List<List<Double>> coordinates = new ArrayList<>();

        // 创建一个基本的多边形，然后添加一些会导致相交的点
        // 这种情况在实际的气象数据中经常出现

        // 主要轮廓
        coordinates.add(Arrays.asList(116.0, 40.0));
        coordinates.add(Arrays.asList(116.3, 40.0));
        coordinates.add(Arrays.asList(116.3, 40.2));
        
        // 添加一个会形成小三角形的突出部分
        coordinates.add(Arrays.asList(116.25, 40.25)); // 突出点
        coordinates.add(Arrays.asList(116.28, 40.22)); // 回折点1
        coordinates.add(Arrays.asList(116.26, 40.24)); // 回折点2，可能形成小区域
        coordinates.add(Arrays.asList(116.27, 40.21)); // 回折点3
        
        coordinates.add(Arrays.asList(116.2, 40.2));
        coordinates.add(Arrays.asList(116.1, 40.3));
        
        // 添加另一个可能相交的区域
        coordinates.add(Arrays.asList(116.05, 40.32)); // 突出点
        coordinates.add(Arrays.asList(116.08, 40.28)); // 回折，可能与前面的线段相交
        coordinates.add(Arrays.asList(116.06, 40.31)); // 小突出
        coordinates.add(Arrays.asList(116.07, 40.29)); // 回折
        
        coordinates.add(Arrays.asList(116.0, 40.3));
        coordinates.add(Arrays.asList(115.9, 40.2));
        
        // 左侧也添加一些复杂的点
        coordinates.add(Arrays.asList(115.85, 40.22)); // 小突出
        coordinates.add(Arrays.asList(115.88, 40.18)); // 回折
        coordinates.add(Arrays.asList(115.86, 40.21)); // 可能形成小三角形
        coordinates.add(Arrays.asList(115.87, 40.19)); // 回折
        
        coordinates.add(Arrays.asList(115.9, 40.1));
        coordinates.add(Arrays.asList(116.0, 40.0)); // 闭合

        return coordinates;
    }

    /**
     * 创建一个简单的相交示例
     */
    public static List<List<Double>> createSimpleIntersectionExample() {
        List<List<Double>> coordinates = new ArrayList<>();
        
        // 创建一个简单的自相交多边形（8字形）
        coordinates.add(Arrays.asList(116.0, 40.0));
        coordinates.add(Arrays.asList(116.1, 40.0));
        coordinates.add(Arrays.asList(116.1, 40.1));
        coordinates.add(Arrays.asList(116.0, 40.1));
        coordinates.add(Arrays.asList(116.1, 40.05)); // 这会造成相交
        coordinates.add(Arrays.asList(116.0, 40.05)); // 形成小区域
        coordinates.add(Arrays.asList(116.0, 40.0)); // 闭合
        
        return coordinates;
    }

    /**
     * 打印坐标详情（用于调试）
     */
    public static void printCoordinateDetails(List<List<Double>> coordinates, String title) {
        log.info("=== {} ===", title);
        for (int i = 0; i < coordinates.size(); i++) {
            List<Double> point = coordinates.get(i);
            log.info("点{}: [{:.6f}, {:.6f}]", i + 1, point.get(0), point.get(1));
        }
        log.info("总点数: {}", coordinates.size());
        log.info("面积: {:.8f} 平方度", GeometryUtils.calculatePolygonArea(coordinates));
        log.info("有效性: {}", GeometryUtils.isValidPolygon(coordinates));
    }
}
