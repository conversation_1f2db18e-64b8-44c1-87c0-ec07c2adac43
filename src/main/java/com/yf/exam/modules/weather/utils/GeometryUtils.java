package com.yf.exam.modules.weather.utils;

import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.geom.impl.CoordinateArraySequence;
import org.locationtech.jts.operation.buffer.BufferOp;
import org.locationtech.jts.operation.buffer.BufferParameters;
import org.locationtech.jts.simplify.DouglasPeuckerSimplifier;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * 几何图形处理工具类
 * 使用JTS库处理几何图形的平滑化、去除尖刺等操作
 */
@Slf4j
public class GeometryUtils {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory();
    
    /**
     * 默认的简化容差（度）
     * 这个值决定了简化的程度，值越大简化越明显
     */
    private static final double DEFAULT_SIMPLIFY_TOLERANCE = 0.001;
    
    /**
     * 默认的缓冲区距离（度）
     * 用于平滑化处理的缓冲区大小
     */
    private static final double DEFAULT_BUFFER_DISTANCE = 0.0005;

    /**
     * 平滑化多边形坐标，去除不规则的"尖刺"
     * 
     * @param coordinates 原始坐标列表 [[lng, lat], [lng, lat], ...]
     * @return 平滑化后的坐标列表
     */
    public static List<List<Double>> smoothPolygonCoordinates(List<List<Double>> coordinates) {
        return smoothPolygonCoordinates(coordinates, DEFAULT_SIMPLIFY_TOLERANCE, DEFAULT_BUFFER_DISTANCE);
    }

    /**
     * 平滑化多边形坐标，去除不规则的"尖刺"
     * 
     * @param coordinates 原始坐标列表 [[lng, lat], [lng, lat], ...]
     * @param simplifyTolerance 简化容差
     * @param bufferDistance 缓冲区距离
     * @return 平滑化后的坐标列表
     */
    public static List<List<Double>> smoothPolygonCoordinates(List<List<Double>> coordinates, 
                                                             double simplifyTolerance, 
                                                             double bufferDistance) {
        try {
            if (coordinates == null || coordinates.size() < 3) {
                log.warn("坐标数据无效，至少需要3个点");
                return coordinates;
            }

            // 转换为JTS Polygon
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            if (polygon == null || !polygon.isValid()) {
                log.warn("无法创建有效的多边形");
                return coordinates;
            }

            // 第一步：使用Douglas-Peucker算法简化多边形，去除冗余点
            Geometry simplified = DouglasPeuckerSimplifier.simplify(polygon, simplifyTolerance);
            
            // 第二步：使用缓冲区操作平滑化边界
            // 先向外缓冲，再向内缓冲，可以平滑尖锐的角度
            Geometry buffered = BufferOp.bufferOp(simplified, bufferDistance, 
                BufferParameters.DEFAULT_QUADRANT_SEGMENTS, BufferParameters.CAP_ROUND);
            Geometry smoothed = BufferOp.bufferOp(buffered, -bufferDistance, 
                BufferParameters.DEFAULT_QUADRANT_SEGMENTS, BufferParameters.CAP_ROUND);

            // 如果平滑化后的几何图形无效，返回简化后的结果
            if (smoothed == null || smoothed.isEmpty() || !smoothed.isValid()) {
                log.warn("平滑化处理失败，使用简化后的结果");
                smoothed = simplified;
            }

            // 转换回坐标列表
            return extractCoordinatesFromGeometry(smoothed);

        } catch (Exception e) {
            log.error("平滑化多边形坐标失败", e);
            return coordinates; // 出错时返回原始坐标
        }
    }

    /**
     * 从坐标列表创建JTS Polygon
     */
    private static Polygon createPolygonFromCoordinates(List<List<Double>> coordinates) {
        try {
            // 确保多边形闭合
            List<List<Double>> closedCoords = new ArrayList<>(coordinates);
            if (!closedCoords.get(0).equals(closedCoords.get(closedCoords.size() - 1))) {
                closedCoords.add(new ArrayList<>(closedCoords.get(0)));
            }

            // 转换为JTS Coordinate数组
            Coordinate[] coords = new Coordinate[closedCoords.size()];
            for (int i = 0; i < closedCoords.size(); i++) {
                List<Double> point = closedCoords.get(i);
                coords[i] = new Coordinate(point.get(0), point.get(1));
            }

            // 创建线性环
            LinearRing shell = GEOMETRY_FACTORY.createLinearRing(coords);
            
            // 创建多边形
            return GEOMETRY_FACTORY.createPolygon(shell);
            
        } catch (Exception e) {
            log.error("创建多边形失败", e);
            return null;
        }
    }

    /**
     * 从JTS几何图形提取坐标
     */
    private static List<List<Double>> extractCoordinatesFromGeometry(Geometry geometry) {
        List<List<Double>> result = new ArrayList<>();
        
        try {
            if (geometry instanceof Polygon) {
                Polygon polygon = (Polygon) geometry;
                Coordinate[] coords = polygon.getExteriorRing().getCoordinates();
                
                for (Coordinate coord : coords) {
                    result.add(Arrays.asList(coord.x, coord.y));
                }
                
            } else if (geometry instanceof MultiPolygon) {
                // 如果是多多边形，取面积最大的一个
                MultiPolygon multiPolygon = (MultiPolygon) geometry;
                Polygon largestPolygon = null;
                double maxArea = 0;
                
                for (int i = 0; i < multiPolygon.getNumGeometries(); i++) {
                    Polygon poly = (Polygon) multiPolygon.getGeometryN(i);
                    double area = poly.getArea();
                    if (area > maxArea) {
                        maxArea = area;
                        largestPolygon = poly;
                    }
                }
                
                if (largestPolygon != null) {
                    Coordinate[] coords = largestPolygon.getExteriorRing().getCoordinates();
                    for (Coordinate coord : coords) {
                        result.add(Arrays.asList(coord.x, coord.y));
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("提取几何图形坐标失败", e);
        }
        
        return result;
    }

    /**
     * 验证多边形是否有效
     */
    public static boolean isValidPolygon(List<List<Double>> coordinates) {
        try {
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            return polygon != null && polygon.isValid();
        } catch (Exception e) {
            log.warn("验证多边形有效性失败", e);
            return false;
        }
    }

    /**
     * 计算多边形面积（平方度）
     */
    public static double calculatePolygonArea(List<List<Double>> coordinates) {
        try {
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            return polygon != null ? polygon.getArea() : 0.0;
        } catch (Exception e) {
            log.warn("计算多边形面积失败", e);
            return 0.0;
        }
    }

    /**
     * 删除线段相交产生的小区域
     * 当多边形边界线相交时，会产生小的三角形或其他小区域，此方法会识别并删除这些区域
     *
     * @param coordinates 原始坐标列表
     * @param minAreaThreshold 最小面积阈值（平方度），小于此值的区域将被删除
     * @return 处理后的坐标列表
     */
    public static List<List<Double>> removeSmallIntersectionAreas(List<List<Double>> coordinates,
                                                                 double minAreaThreshold) {
        try {
            if (coordinates == null || coordinates.size() < 3) {
                return coordinates;
            }

            // 创建多边形
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            if (polygon == null) {
                return coordinates;
            }

            // 如果多边形无效（通常是因为自相交），使用更强力的修复方法
            if (!polygon.isValid()) {
                log.debug("检测到无效多边形（可能自相交），使用强力修复方法");
                return fixSelfIntersectingPolygon(coordinates, minAreaThreshold);
            }

            // 对于有效多边形，使用缓冲区操作来删除小的突出部分
            return applyBufferCleaning(polygon, minAreaThreshold, coordinates);

        } catch (Exception e) {
            log.error("删除小相交区域失败", e);
            return coordinates;
        }
    }

    /**
     * 修复自相交多边形
     */
    private static List<List<Double>> fixSelfIntersectingPolygon(List<List<Double>> coordinates,
                                                               double minAreaThreshold) {
        try {
            // 方法1: 使用更大的缓冲区来强制修复自相交
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            if (polygon == null) {
                return coordinates;
            }

            // 使用较大的向内缓冲区来消除自相交
            double[] bufferSizes = {-0.0005, -0.001, -0.002}; // 逐步增大缓冲区

            for (double bufferSize : bufferSizes) {
                try {
                    Geometry buffered = polygon.buffer(bufferSize);
                    if (buffered != null && !buffered.isEmpty()) {
                        // 恢复原始大小
                        Geometry restored = buffered.buffer(-bufferSize);
                        if (restored != null && !restored.isEmpty() && restored.isValid()) {
                            return processMultiPolygonResult(restored, minAreaThreshold, coordinates);
                        }
                    }
                } catch (Exception e) {
                    log.debug("缓冲区大小 {} 处理失败，尝试下一个", bufferSize);
                }
            }

            // 方法2: 如果缓冲区方法失败，尝试使用凸包
            log.debug("缓冲区方法失败，尝试使用凸包方法");
            Geometry convexHull = polygon.convexHull();
            if (convexHull != null && convexHull.isValid() && convexHull instanceof Polygon) {
                // 凸包可能过于简化，所以只在原多边形严重自相交时使用
                double originalArea = polygon.getArea();
                double convexArea = convexHull.getArea();
                if (convexArea > 0 && (convexArea / originalArea) < 2.0) { // 凸包面积不超过原面积的2倍
                    return extractCoordinatesFromGeometry(convexHull);
                }
            }

            // 方法3: 最后的备选方案 - 使用简化算法
            log.debug("尝试使用简化算法处理自相交");
            Geometry simplified = DouglasPeuckerSimplifier.simplify(polygon, 0.005); // 更大的简化容差
            if (simplified != null && simplified.isValid()) {
                return extractCoordinatesFromGeometry(simplified);
            }

            return coordinates; // 所有方法都失败，返回原始坐标

        } catch (Exception e) {
            log.error("修复自相交多边形失败", e);
            return coordinates;
        }
    }

    /**
     * 应用缓冲区清理
     */
    private static List<List<Double>> applyBufferCleaning(Polygon polygon, double minAreaThreshold,
                                                         List<List<Double>> originalCoordinates) {
        try {
            // 使用多级缓冲区处理
            double[] bufferSizes = {-0.0001, -0.0002, -0.0005}; // 从小到大尝试

            for (double bufferSize : bufferSizes) {
                try {
                    Geometry buffered = polygon.buffer(bufferSize);
                    if (buffered != null && !buffered.isEmpty()) {
                        Geometry restored = buffered.buffer(-bufferSize);
                        if (restored != null && !restored.isEmpty() && restored.isValid()) {
                            List<List<Double>> result = processMultiPolygonResult(restored, minAreaThreshold, originalCoordinates);
                            if (result != originalCoordinates) { // 如果处理成功
                                return result;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.debug("缓冲区大小 {} 处理失败", bufferSize);
                }
            }

            return originalCoordinates;

        } catch (Exception e) {
            log.error("应用缓冲区清理失败", e);
            return originalCoordinates;
        }
    }

    /**
     * 处理多多边形结果
     */
    private static List<List<Double>> processMultiPolygonResult(Geometry geometry, double minAreaThreshold,
                                                               List<List<Double>> fallbackCoordinates) {
        try {
            if (geometry instanceof MultiPolygon) {
                MultiPolygon multiPolygon = (MultiPolygon) geometry;
                List<Polygon> validPolygons = new ArrayList<>();

                for (int i = 0; i < multiPolygon.getNumGeometries(); i++) {
                    Polygon poly = (Polygon) multiPolygon.getGeometryN(i);
                    if (poly.getArea() >= minAreaThreshold) {
                        validPolygons.add(poly);
                    } else {
                        log.debug("删除小区域，面积: {} 平方度", poly.getArea());
                    }
                }

                if (validPolygons.isEmpty()) {
                    return fallbackCoordinates;
                }

                // 选择面积最大的多边形
                Polygon largestPolygon = validPolygons.stream()
                    .max(Comparator.comparing(p -> p.getArea()))
                    .orElse(null);

                if (largestPolygon != null) {
                    return extractCoordinatesFromGeometry(largestPolygon);
                }
            } else if (geometry instanceof Polygon) {
                Polygon polygon = (Polygon) geometry;
                if (polygon.getArea() >= minAreaThreshold) {
                    return extractCoordinatesFromGeometry(polygon);
                }
            }

            return extractCoordinatesFromGeometry(geometry);

        } catch (Exception e) {
            log.error("处理多多边形结果失败", e);
            return fallbackCoordinates;
        }
    }

    /**
     * 删除线段相交产生的小区域（使用默认阈值）
     * 默认最小面积阈值为 0.0001 平方度
     */
    public static List<List<Double>> removeSmallIntersectionAreas(List<List<Double>> coordinates) {
        return removeSmallIntersectionAreas(coordinates, 0.0001);
    }

    /**
     * 强力清理自相交多边形
     * 使用更激进的方法来处理严重的自相交问题
     *
     * @param coordinates 原始坐标列表
     * @return 处理后的坐标列表
     */
    public static List<List<Double>> aggressiveCleanIntersections(List<List<Double>> coordinates) {
        try {
            if (coordinates == null || coordinates.size() < 3) {
                return coordinates;
            }

            // 创建多边形
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            if (polygon == null) {
                return coordinates;
            }

            // 方法1: 使用大缓冲区强制清理
            double[] aggressiveBuffers = {-0.001, -0.002, -0.005, -0.01}; // 更大的缓冲区

            for (double bufferSize : aggressiveBuffers) {
                try {
                    Geometry buffered = polygon.buffer(bufferSize);
                    if (buffered != null && !buffered.isEmpty()) {
                        Geometry restored = buffered.buffer(-bufferSize);
                        if (restored != null && !restored.isEmpty() && restored.isValid()) {
                            // 只保留最大的多边形
                            if (restored instanceof MultiPolygon) {
                                MultiPolygon mp = (MultiPolygon) restored;
                                Polygon largest = null;
                                double maxArea = 0;

                                for (int i = 0; i < mp.getNumGeometries(); i++) {
                                    Polygon p = (Polygon) mp.getGeometryN(i);
                                    if (p.getArea() > maxArea) {
                                        maxArea = p.getArea();
                                        largest = p;
                                    }
                                }

                                if (largest != null) {
                                    log.debug("强力清理成功，使用缓冲区大小: {}", bufferSize);
                                    return extractCoordinatesFromGeometry(largest);
                                }
                            } else if (restored instanceof Polygon) {
                                log.debug("强力清理成功，使用缓冲区大小: {}", bufferSize);
                                return extractCoordinatesFromGeometry(restored);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.debug("强力缓冲区 {} 失败，尝试下一个", bufferSize);
                }
            }

            // 方法2: 如果缓冲区方法失败，使用凸包
            log.debug("尝试使用凸包方法进行强力清理");
            try {
                Geometry convexHull = polygon.convexHull();
                if (convexHull != null && convexHull.isValid()) {
                    return extractCoordinatesFromGeometry(convexHull);
                }
            } catch (Exception e) {
                log.debug("凸包方法失败");
            }

            // 方法3: 最后的备选方案 - 重建简化多边形
            log.debug("使用重建方法进行强力清理");
            return rebuildSimplifiedPolygon(coordinates);

        } catch (Exception e) {
            log.error("强力清理自相交失败", e);
            return coordinates;
        }
    }

    /**
     * 重建简化多边形
     * 通过采样关键点来重建一个简化的多边形
     */
    private static List<List<Double>> rebuildSimplifiedPolygon(List<List<Double>> coordinates) {
        try {
            if (coordinates.size() <= 10) {
                return coordinates; // 点太少，不需要重建
            }

            List<List<Double>> simplified = new ArrayList<>();

            // 采样策略：每隔几个点取一个，确保保留关键的转折点
            int step = Math.max(1, coordinates.size() / 20); // 最多保留20个关键点

            for (int i = 0; i < coordinates.size() - 1; i += step) { // -1 避免重复闭合点
                simplified.add(new ArrayList<>(coordinates.get(i)));
            }

            // 确保闭合
            if (!simplified.isEmpty() && !simplified.get(0).equals(simplified.get(simplified.size() - 1))) {
                simplified.add(new ArrayList<>(simplified.get(0)));
            }

            // 验证重建的多边形
            if (simplified.size() >= 4) { // 至少需要4个点（包括闭合点）
                Polygon rebuilt = createPolygonFromCoordinates(simplified);
                if (rebuilt != null && rebuilt.isValid()) {
                    log.debug("重建多边形成功，从 {} 个点简化为 {} 个点", coordinates.size(), simplified.size());
                    return simplified;
                }
            }

            return coordinates; // 重建失败，返回原始坐标

        } catch (Exception e) {
            log.error("重建简化多边形失败", e);
            return coordinates;
        }
    }

    /**
     * 综合处理多边形：删除小相交区域 + 平滑化
     *
     * @param coordinates 原始坐标列表
     * @return 处理后的坐标列表
     */
    public static List<List<Double>> processPolygonComprehensively(List<List<Double>> coordinates) {
        try {
            if (coordinates == null || coordinates.size() < 3) {
                return coordinates;
            }

            // 第一步：尝试常规方法删除小的相交区域
            List<List<Double>> cleanedCoordinates = removeSmallIntersectionAreas(coordinates);

            // 检查是否还有自相交问题
            boolean stillHasIssues = false;
            try {
                Polygon testPolygon = createPolygonFromCoordinates(cleanedCoordinates);
                if (testPolygon == null || !testPolygon.isValid()) {
                    stillHasIssues = true;
                }
            } catch (Exception e) {
                stillHasIssues = true;
            }

            // 如果常规方法无效，使用强力清理
            if (stillHasIssues) {
                log.debug("常规清理方法无效，使用强力清理方法");
                cleanedCoordinates = aggressiveCleanIntersections(coordinates);
            }

            // 第二步：平滑化处理
            List<List<Double>> smoothedCoordinates = smoothPolygonCoordinates(cleanedCoordinates);

            // 验证最终结果
            boolean finalValid = isValidPolygon(smoothedCoordinates);
            if (!finalValid) {
                log.warn("最终结果仍然无效，返回清理后的坐标");
                return cleanedCoordinates;
            }

            log.debug("综合处理完成: 原始点数={}, 清理后点数={}, 平滑化后点数={}, 最终有效={}",
                coordinates.size(), cleanedCoordinates.size(), smoothedCoordinates.size(), finalValid);

            return smoothedCoordinates;

        } catch (Exception e) {
            log.error("综合处理多边形失败", e);
            return coordinates;
        }
    }

    /**
     * 超强力综合处理（用于处理最困难的情况）
     *
     * @param coordinates 原始坐标列表
     * @return 处理后的坐标列表
     */
    public static List<List<Double>> processPolygonUltraComprehensively(List<List<Double>> coordinates) {
        try {
            if (coordinates == null || coordinates.size() < 3) {
                return coordinates;
            }

            // 直接使用强力清理
            List<List<Double>> cleanedCoordinates = aggressiveCleanIntersections(coordinates);

            // 然后进行平滑化
            List<List<Double>> smoothedCoordinates = smoothPolygonCoordinates(cleanedCoordinates);

            // 如果平滑化后仍有问题，返回清理后的结果
            if (!isValidPolygon(smoothedCoordinates)) {
                log.warn("平滑化后仍有问题，返回强力清理的结果");
                return cleanedCoordinates;
            }

            log.debug("超强力处理完成: 原始点数={}, 处理后点数={}",
                coordinates.size(), smoothedCoordinates.size());

            return smoothedCoordinates;

        } catch (Exception e) {
            log.error("超强力处理多边形失败", e);
            return coordinates;
        }
    }
}
