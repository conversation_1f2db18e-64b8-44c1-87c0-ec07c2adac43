package com.yf.exam.modules.weather.utils;

import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.geom.impl.CoordinateArraySequence;
import org.locationtech.jts.operation.buffer.BufferOp;
import org.locationtech.jts.operation.buffer.BufferParameters;
import org.locationtech.jts.simplify.DouglasPeuckerSimplifier;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 几何图形处理工具类
 * 使用JTS库处理几何图形的平滑化、去除尖刺等操作
 */
@Slf4j
public class GeometryUtils {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory();
    
    /**
     * 默认的简化容差（度）
     * 这个值决定了简化的程度，值越大简化越明显
     */
    private static final double DEFAULT_SIMPLIFY_TOLERANCE = 0.001;
    
    /**
     * 默认的缓冲区距离（度）
     * 用于平滑化处理的缓冲区大小
     */
    private static final double DEFAULT_BUFFER_DISTANCE = 0.0005;

    /**
     * 平滑化多边形坐标，去除不规则的"尖刺"
     * 
     * @param coordinates 原始坐标列表 [[lng, lat], [lng, lat], ...]
     * @return 平滑化后的坐标列表
     */
    public static List<List<Double>> smoothPolygonCoordinates(List<List<Double>> coordinates) {
        return smoothPolygonCoordinates(coordinates, DEFAULT_SIMPLIFY_TOLERANCE, DEFAULT_BUFFER_DISTANCE);
    }

    /**
     * 平滑化多边形坐标，去除不规则的"尖刺"
     * 
     * @param coordinates 原始坐标列表 [[lng, lat], [lng, lat], ...]
     * @param simplifyTolerance 简化容差
     * @param bufferDistance 缓冲区距离
     * @return 平滑化后的坐标列表
     */
    public static List<List<Double>> smoothPolygonCoordinates(List<List<Double>> coordinates, 
                                                             double simplifyTolerance, 
                                                             double bufferDistance) {
        try {
            if (coordinates == null || coordinates.size() < 3) {
                log.warn("坐标数据无效，至少需要3个点");
                return coordinates;
            }

            // 转换为JTS Polygon
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            if (polygon == null || !polygon.isValid()) {
                log.warn("无法创建有效的多边形");
                return coordinates;
            }

            // 第一步：使用Douglas-Peucker算法简化多边形，去除冗余点
            Geometry simplified = DouglasPeuckerSimplifier.simplify(polygon, simplifyTolerance);
            
            // 第二步：使用缓冲区操作平滑化边界
            // 先向外缓冲，再向内缓冲，可以平滑尖锐的角度
            Geometry buffered = BufferOp.bufferOp(simplified, bufferDistance, 
                BufferParameters.DEFAULT_QUADRANT_SEGMENTS, BufferParameters.CAP_ROUND);
            Geometry smoothed = BufferOp.bufferOp(buffered, -bufferDistance, 
                BufferParameters.DEFAULT_QUADRANT_SEGMENTS, BufferParameters.CAP_ROUND);

            // 如果平滑化后的几何图形无效，返回简化后的结果
            if (smoothed == null || smoothed.isEmpty() || !smoothed.isValid()) {
                log.warn("平滑化处理失败，使用简化后的结果");
                smoothed = simplified;
            }

            // 转换回坐标列表
            return extractCoordinatesFromGeometry(smoothed);

        } catch (Exception e) {
            log.error("平滑化多边形坐标失败", e);
            return coordinates; // 出错时返回原始坐标
        }
    }

    /**
     * 从坐标列表创建JTS Polygon
     */
    private static Polygon createPolygonFromCoordinates(List<List<Double>> coordinates) {
        try {
            // 确保多边形闭合
            List<List<Double>> closedCoords = new ArrayList<>(coordinates);
            if (!closedCoords.get(0).equals(closedCoords.get(closedCoords.size() - 1))) {
                closedCoords.add(new ArrayList<>(closedCoords.get(0)));
            }

            // 转换为JTS Coordinate数组
            Coordinate[] coords = new Coordinate[closedCoords.size()];
            for (int i = 0; i < closedCoords.size(); i++) {
                List<Double> point = closedCoords.get(i);
                coords[i] = new Coordinate(point.get(0), point.get(1));
            }

            // 创建线性环
            LinearRing shell = GEOMETRY_FACTORY.createLinearRing(coords);
            
            // 创建多边形
            return GEOMETRY_FACTORY.createPolygon(shell);
            
        } catch (Exception e) {
            log.error("创建多边形失败", e);
            return null;
        }
    }

    /**
     * 从JTS几何图形提取坐标
     */
    private static List<List<Double>> extractCoordinatesFromGeometry(Geometry geometry) {
        List<List<Double>> result = new ArrayList<>();
        
        try {
            if (geometry instanceof Polygon) {
                Polygon polygon = (Polygon) geometry;
                Coordinate[] coords = polygon.getExteriorRing().getCoordinates();
                
                for (Coordinate coord : coords) {
                    result.add(Arrays.asList(coord.x, coord.y));
                }
                
            } else if (geometry instanceof MultiPolygon) {
                // 如果是多多边形，取面积最大的一个
                MultiPolygon multiPolygon = (MultiPolygon) geometry;
                Polygon largestPolygon = null;
                double maxArea = 0;
                
                for (int i = 0; i < multiPolygon.getNumGeometries(); i++) {
                    Polygon poly = (Polygon) multiPolygon.getGeometryN(i);
                    double area = poly.getArea();
                    if (area > maxArea) {
                        maxArea = area;
                        largestPolygon = poly;
                    }
                }
                
                if (largestPolygon != null) {
                    Coordinate[] coords = largestPolygon.getExteriorRing().getCoordinates();
                    for (Coordinate coord : coords) {
                        result.add(Arrays.asList(coord.x, coord.y));
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("提取几何图形坐标失败", e);
        }
        
        return result;
    }

    /**
     * 验证多边形是否有效
     */
    public static boolean isValidPolygon(List<List<Double>> coordinates) {
        try {
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            return polygon != null && polygon.isValid();
        } catch (Exception e) {
            log.warn("验证多边形有效性失败", e);
            return false;
        }
    }

    /**
     * 计算多边形面积（平方度）
     */
    public static double calculatePolygonArea(List<List<Double>> coordinates) {
        try {
            Polygon polygon = createPolygonFromCoordinates(coordinates);
            return polygon != null ? polygon.getArea() : 0.0;
        } catch (Exception e) {
            log.warn("计算多边形面积失败", e);
            return 0.0;
        }
    }
}
