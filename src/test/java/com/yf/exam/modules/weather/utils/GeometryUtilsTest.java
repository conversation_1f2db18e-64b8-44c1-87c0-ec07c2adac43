package com.yf.exam.modules.weather.utils;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * GeometryUtils 测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class GeometryUtilsTest {

    @Test
    public void testSmoothPolygonCoordinates() {
        // 创建一个带有"尖刺"的多边形坐标
        List<List<Double>> coordinates = new ArrayList<>();
        coordinates.add(Arrays.asList(116.0, 40.0));
        coordinates.add(Arrays.asList(116.1, 40.0));
        coordinates.add(Arrays.asList(116.05, 40.05)); // 尖刺点
        coordinates.add(Arrays.asList(116.1, 40.1));
        coordinates.add(Arrays.asList(116.0, 40.1));
        coordinates.add(Arrays.asList(116.0, 40.0)); // 闭合

        // 执行平滑化
        List<List<Double>> smoothed = GeometryUtils.smoothPolygonCoordinates(coordinates);

        // 验证结果
        assertNotNull("平滑化结果不应为null", smoothed);
        assertFalse("平滑化结果不应为空", smoothed.isEmpty());
        assertTrue("平滑化后应该是闭合的多边形", 
            smoothed.get(0).equals(smoothed.get(smoothed.size() - 1)));
        
        System.out.println("原始坐标点数: " + coordinates.size());
        System.out.println("平滑化后坐标点数: " + smoothed.size());
    }

    @Test
    public void testIsValidPolygon() {
        // 测试有效多边形
        List<List<Double>> validCoordinates = new ArrayList<>();
        validCoordinates.add(Arrays.asList(116.0, 40.0));
        validCoordinates.add(Arrays.asList(116.1, 40.0));
        validCoordinates.add(Arrays.asList(116.1, 40.1));
        validCoordinates.add(Arrays.asList(116.0, 40.1));
        validCoordinates.add(Arrays.asList(116.0, 40.0));

        assertTrue("应该是有效的多边形", GeometryUtils.isValidPolygon(validCoordinates));

        // 测试无效多边形（点数不足）
        List<List<Double>> invalidCoordinates = new ArrayList<>();
        invalidCoordinates.add(Arrays.asList(116.0, 40.0));
        invalidCoordinates.add(Arrays.asList(116.1, 40.0));

        assertFalse("点数不足的多边形应该无效", GeometryUtils.isValidPolygon(invalidCoordinates));
    }

    @Test
    public void testCalculatePolygonArea() {
        // 创建一个简单的矩形
        List<List<Double>> coordinates = new ArrayList<>();
        coordinates.add(Arrays.asList(116.0, 40.0));
        coordinates.add(Arrays.asList(116.1, 40.0));
        coordinates.add(Arrays.asList(116.1, 40.1));
        coordinates.add(Arrays.asList(116.0, 40.1));
        coordinates.add(Arrays.asList(116.0, 40.0));

        double area = GeometryUtils.calculatePolygonArea(coordinates);
        
        assertTrue("面积应该大于0", area > 0);
        System.out.println("多边形面积: " + area + " 平方度");
    }

    @Test
    public void testSmoothPolygonWithSmallCoordinateSet() {
        // 测试坐标点较少的情况
        List<List<Double>> coordinates = new ArrayList<>();
        coordinates.add(Arrays.asList(116.0, 40.0));
        coordinates.add(Arrays.asList(116.1, 40.0));
        coordinates.add(Arrays.asList(116.1, 40.1));
        coordinates.add(Arrays.asList(116.0, 40.1));

        List<List<Double>> smoothed = GeometryUtils.smoothPolygonCoordinates(coordinates);

        assertNotNull("平滑化结果不应为null", smoothed);
        assertFalse("平滑化结果不应为空", smoothed.isEmpty());
    }

    @Test
    public void testSmoothPolygonWithNullInput() {
        // 测试null输入
        List<List<Double>> result = GeometryUtils.smoothPolygonCoordinates(null);
        assertNull("null输入应该返回null", result);

        // 测试空列表输入
        List<List<Double>> emptyList = new ArrayList<>();
        List<List<Double>> result2 = GeometryUtils.smoothPolygonCoordinates(emptyList);
        assertEquals("空列表输入应该返回原列表", emptyList, result2);
    }
}
